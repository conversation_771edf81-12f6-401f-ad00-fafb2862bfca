<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bestseller</groupId>
    <artifactId>file-bundle-generator</artifactId>
    <version>1.0-SNAPSHOT</version>

    <name>File Bundle Generator</name>
    <description>BESTSELLER File Bundle Generator</description>
    <packaging>jar</packaging>

    <scm>
        <connection>scm:git:*****************:bestseller-ecom/file-bundle-generator.git</connection>
        <developerConnection>scm:git:*****************:bestseller-ecom/file-bundle-generator.git</developerConnection>
        <url>http://bitbucket.org/bestseller-ecom/file-bundle-generator</url>
        <tag>file-bundle-generator-1.0.0</tag>
    </scm>

    <properties>
        <!-- Java version -->
        <java.version>21</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>

        <!-- Maven plugin versions -->
        <maven.compiler.plugin.version>3.13.0</maven.compiler.plugin.version>
        <maven.shade.plugin.version>3.6.0</maven.shade.plugin.version>
        <maven.surefire.plugin.version>3.5.3</maven.surefire.plugin.version>
        <maven.failsafe.plugin.version>3.5.3</maven.failsafe.plugin.version>
        <maven.checkstyle.plugin.version>3.5.0</maven.checkstyle.plugin.version>
        <jsonschema2pojo-maven-plugin.version>1.2.2</jsonschema2pojo-maven-plugin.version>

        <!-- AWS dependencies -->
        <aws-java-sdk-s3.version>1.12.778</aws-java-sdk-s3.version>
        <aws-lambda-java-core.version>1.3.0</aws-lambda-java-core.version>
        <aws-lambda-java-events.version>3.14.0</aws-lambda-java-events.version>

        <!-- Logging dependencies -->
        <logback.version>1.5.15</logback.version>
        <logstash-logback-encoder.version>8.0</logstash-logback-encoder.version>
        <slf4j.version>2.0.16</slf4j.version>

        <!-- JSON and HTTP dependencies -->
        <jackson.version>2.15.2</jackson.version>
        <httpclient.version>4.5.14</httpclient.version>
        <commons-lang.version>2.6</commons-lang.version>
        <commons-io.version>2.17.0</commons-io.version>

        <!-- Test dependencies -->
        <junit.version>5.11.4</junit.version>
        <mockito.version>5.18.0</mockito.version>
        <awaitility.version>4.2.2</awaitility.version>
        <spring-test.version>6.2.1</spring-test.version>

        <!-- BDD Test dependencies -->
        <serenity.jbehave.version>4.1.4</serenity.jbehave.version>
        <serenity.version>4.2.8</serenity.version>
        <jbehave.version>5.2.0</jbehave.version>

        <!-- Other dependencies -->
        <bse-checkstyle.version>1.0.19</bse-checkstyle.version>
        <byte-buddy.version>1.15.11</byte-buddy.version>

        <!-- Test configuration -->
        <skipUnitTests>false</skipUnitTests>
    </properties>

    <build>
        <plugins>
            <!-- package for the artifact that will be uploaded to aws lambda  -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>${maven.shade.plugin.version}</version>
                <configuration>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- JSON Schema to POJO plugin -->
            <plugin>
                <groupId>org.jsonschema2pojo</groupId>
                <artifactId>jsonschema2pojo-maven-plugin</artifactId>
                <version>${jsonschema2pojo-maven-plugin.version}</version>
                <configuration>
                    <includeHashcodeAndEquals>false</includeHashcodeAndEquals>
                    <useBigDecimals>true</useBigDecimals>
                    <generateBuilders>true</generateBuilders>
                    <dateTimeType>java.time.ZonedDateTime</dateTimeType>
                    <dateType>java.time.ZonedDateTime</dateType>
                    <formatDates>true</formatDates>
                    <formatDateTimes>true</formatDateTimes>
                    <includeConstructors>true</includeConstructors>
                    <includeAdditionalProperties>false</includeAdditionalProperties>
                </configuration>
                <executions>
                    <execution>
                        <id>schema.json-generate</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <sourceDirectory>${project.basedir}/src/main/resources/schema/json/</sourceDirectory>
                            <targetPackage>fbg.generated.elasticsearch.model</targetPackage>
                            <outputDirectory>${project.basedir}/src/main/gensrc/</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- For running integration tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>${maven.failsafe.plugin.version}</version>
                <executions>
                    <execution>
                        <id>default</id>
                        <goals>
                            <goal>integration-test</goal>
                        </goals>
                        <configuration>
                            <includes>
                                <include>**/FunctionalTestSuite.java</include>
                            </includes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${maven.checkstyle.plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.bestseller</groupId>
                            <artifactId>bse-checkstyle</artifactId>
                            <version>${bse-checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <configLocation>com/bestseller/checkstyle.xml</configLocation>
                        <suppressionsLocation>${basedir}/checkstyle-suppressions.xml</suppressionsLocation>
                        <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    </configuration>
                </plugin>
                <!-- For running unit tests -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven.surefire.plugin.version}</version>
                    <configuration>
                        <skipTests>${skipUnitTests}</skipTests>
                        <!-- No special JVM arguments needed for modern testing -->
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>${aws-java-sdk-s3.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-lambda-java-core</artifactId>
            <version>${aws-lambda-java-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-lambda-java-events</artifactId>
            <version>${aws-lambda-java-events.version}</version>
        </dependency>
        <!-- Logging dependencies -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash-logback-encoder.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons-lang.version}</version>
        </dependency>
        <!-- JSON dependencies -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <!-- Test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring-test.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>${awaitility.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- BDD Test dependencies -->
        <dependency>
            <groupId>net.serenity-bdd</groupId>
            <artifactId>serenity-jbehave</artifactId>
            <version>${serenity.jbehave.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jbehave</groupId>
            <artifactId>jbehave-core</artifactId>
            <version>${jbehave.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jbehave</groupId>
            <artifactId>jbehave-spring</artifactId>
            <version>${jbehave.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.serenity-bdd</groupId>
            <artifactId>serenity-core</artifactId>
            <version>${serenity.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.serenity-bdd</groupId>
            <artifactId>serenity-junit</artifactId>
            <version>${serenity.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.serenity-bdd</groupId>
            <artifactId>serenity-spring</artifactId>
            <version>${serenity.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- Force consistent Jackson versions -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-cbor</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${byte-buddy.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>${byte-buddy.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
package fbg.functions;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.S3Event;
import com.amazonaws.services.lambda.runtime.events.models.s3.S3EventNotification;
import fbg.context.BeanFactory;
import fbg.generated.elasticsearch.model.ArchiveContent;
import fbg.service.ArchiveProcessService;
import fbg.service.ElasticSearchService;
import fbg.service.S3Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.function.Predicate;

/**
 * Function to handle S3 bucket event.
 */
public class S3EventHandler implements RequestHandler<S3Event, String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(S3EventHandler.class);

    private S3Service s3Service = BeanFactory.getInstance().getS3Service();

    private ArchiveProcessService archiveProcessService = BeanFactory.getInstance().getArchiveProcessService();

    private ElasticSearchService elasticSearchService = BeanFactory.getInstance().getElasticSearchService();

    private Predicate<S3EventNotification.S3EventNotificationRecord> filterRecords = (s3EventNotificationRecord) ->
            Objects.nonNull(s3EventNotificationRecord) && Objects.nonNull(s3EventNotificationRecord.getS3());
    private Predicate<ArchiveContent> filterProcessableArchive = (archiveContent) ->
            Objects.nonNull(archiveContent.getType()) && Objects.nonNull(archiveContent.getOrders());

    @Override
    public String handleRequest(S3Event s3event, Context context) {
        Objects.requireNonNull(s3event, "S3Event is null");
        Objects.requireNonNull(s3event.getRecords(), "S3Event.records are null");
        s3event.getRecords()
                .parallelStream()
                .filter(filterRecords)
                .map(record -> {
                    LOGGER.info("####BUCKETNAME {}", record.getS3().getBucket().getName());
                    return s3Service.getObjectFromBucket(record.getS3().getBucket().getName(), record.getS3().getObject().getKey());
                })
                .map(archiveProcessService::processS3Object)
                .filter(filterProcessableArchive)
                .forEach(elasticSearchService::putOrderDocument);
        return "OK";
    }
}

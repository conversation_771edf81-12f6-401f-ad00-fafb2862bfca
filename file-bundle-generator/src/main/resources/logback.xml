<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Console appender with JSON formatting for AWS Lambda -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <!-- Include AWS Lambda request ID in logs -->
            <includeContext>true</includeContext>
            <includeMdc>true</includeMdc>
            <customFields>{"service":"file-bundle-generator"}</customFields>
            <!-- Add timestamp in ISO format -->
            <timestampPattern>yyyy-MM-dd'T'HH:mm:ss.SSSZ</timestampPattern>
        </encoder>
    </appender>

    <!-- Set logging level to DEBUG for the application -->
    <logger name="fbg" level="DEBUG"/>
    
    <!-- Reduce noise from AWS SDK -->
    <logger name="com.amazonaws" level="WARN"/>
    <logger name="software.amazon" level="WARN"/>
    
    <!-- Root logger configuration -->
    <root level="DEBUG">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>

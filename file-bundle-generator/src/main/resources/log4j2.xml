<?xml version="1.0" encoding="UTF-8"?>
<Configuration packages="com.amazonaws.services.lambda.runtime.log4j2.LambdaAppender">
    <Appenders>
        <Lambda name="Lambda">
            <JsonTemplateLayout>
                <EventTemplateAdditionalField key="timestamp" value="$${date:yyyy-MM-dd'T'HH:mm:ss.SSSZ}" />
                <EventTemplateAdditionalField key="requestId" value="$${mdc:AWSRequestId}" />
                <EventTemplateAdditionalField key="level" value="$${level}" />
                <EventTemplateAdditionalField key="logger" value="$${logger}" />
                <EventTemplateAdditionalField key="message" value="$${message}" />
                <EventTemplateAdditionalField key="thread" value="$${thread}" />
                <EventTemplateAdditionalField key="source" value="$${source.className}:$${source.lineNumber}" />
            </JsonTemplateLayout>
        </Lambda>
    </Appenders>
    <Loggers>
        <Root level="debug">
            <AppenderRef ref="Lambda" />
        </Root>
    </Loggers>
</Configuration>
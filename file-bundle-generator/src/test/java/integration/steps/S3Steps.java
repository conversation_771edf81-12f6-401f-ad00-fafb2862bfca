package integration.steps;

import com.amazonaws.services.lambda.runtime.events.S3Event;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.lambda.runtime.events.models.s3.S3EventNotification;
import com.amazonaws.services.s3.model.DeleteObjectRequest;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import fbg.configuration.HwcConfiguration;
import fbg.configuration.ImwcConfiguration;
import fbg.configuration.OmsConfiguration;
import fbg.functions.S3EventHandler;
import integration.FunctionalTestSession;
import integration.model.InMemoryArchive;
import integration.service.ArchiveGeneratorService;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.jbehave.core.annotations.Given;
import org.jbehave.core.annotations.When;
import org.jbehave.core.model.ExamplesTable;
import org.jbehave.core.steps.Parameters;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * Steps that involve s3.
 */
public class S3Steps {
    private static final Logger LOGGER = LoggerFactory.getLogger(S3Steps.class);
    private ConcurrentLinkedQueue<PutObjectRequest> putObjectRequests = new ConcurrentLinkedQueue<>();
    private ArchiveGeneratorService archiveGeneratorService = FunctionalTestSession.getInstance().getArchiveGeneratorService();
    private AmazonS3 amazonS3 = FunctionalTestSession.getInstance().getAmazonS3();

    /**
     * Upload the generated archives.
     *
     * @param archives
     */
    @Given("multiple uploaded archives: $archives")
    public void uploadArchives(ExamplesTable archives) {
        archiveGeneratorService.initKeyMaps();
        archives.getRowsAsParameters().forEach(
                (Parameters parameters) -> {
                    String fileNameKey = parameters.valueAs("fileNameKey", String.class);
                    String bucketKey = parameters.valueAs("bucketKey", String.class);
                    String archiveType = parameters.valueAs("archiveType", String.class);
                    String orderIdKeys = parameters.valueAs("orderIdKeys", String.class);
                    InMemoryArchive inMemoryArchive = archiveGeneratorService.generateInMemoryArchive(fileNameKey,
                            bucketKey, archiveType, orderIdKeys.split(","));
                    PutObjectRequest putObjectRequest = createPutObjectRequest(inMemoryArchive);
                    amazonS3.putObject(putObjectRequest);
                    putObjectRequests.add(putObjectRequest);
                }
        );
        archiveGeneratorService.logKeys();
    }

    /**
     * Simulate the trigger of the s3 object notifications.
     */
    @When("S3 object creation notifications have been triggered")
    public void triggerS3ObjectCreationNotifications() {
        putObjectRequests.parallelStream().forEach(
                (putObjectRequest) -> {
                    new S3EventHandler().handleRequest(createS3event(putObjectRequest), null);
                    putObjectRequests.remove(putObjectRequest);
                }
        );
    }

    private S3Event createS3event(PutObjectRequest putObjectRequest) {
        List<S3EventNotification.S3EventNotificationRecord> records = new ArrayList<>();
        S3EventNotification.S3BucketEntity bucket = new S3EventNotification.S3BucketEntity(putObjectRequest.getBucketName(), null, null);
        S3EventNotification.S3ObjectEntity object = new S3EventNotification.S3ObjectEntity(putObjectRequest.getKey(), null, null, null, null);
        S3EventNotification.S3Entity s3 = new S3EventNotification.S3Entity(null, bucket, object, null);
        S3EventNotification.S3EventNotificationRecord s3EventNotificationRecord =
                new S3EventNotification.S3EventNotificationRecord(null, null, null, null, null, null, null, s3, null);
        records.add(s3EventNotificationRecord);
        S3Event s3Event = new S3Event(records);
        return s3Event;
    }

    /**
     * Delete all the objects in the buckets that will be used in the integration test.
     */
    @Given("Buckets are empty")
    public void emptyBuckets() {
        LOGGER.info("Starting to delete all thee key of the buckets ");
        clearBucket(HwcConfiguration.getInstance().getBucketName());

        clearBucket(OmsConfiguration.getInstance().getBucketName());

        clearBucket(ImwcConfiguration.getInstance().getBucketName());

        LOGGER.info("Buckets are now empty");

    }

    private void clearBucket(String bucketName) {
        final ListObjectsV2Request req = new ListObjectsV2Request().withBucketName(bucketName);
        ListObjectsV2Result result = amazonS3.listObjectsV2(req);
        for (S3ObjectSummary objectSummary : result.getObjectSummaries()) {
            amazonS3.deleteObject(new DeleteObjectRequest(bucketName, objectSummary.getKey()));
        }
    }

    private PutObjectRequest createPutObjectRequest(InMemoryArchive inMemoryArchive) {
        try {
            final byte[] bytes = inMemoryArchive.getContent().getBytes(StandardCharsets.UTF_8.name());

            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.reset();
            messageDigest.update(bytes);
            byte[] resultByte = messageDigest.digest();
            String streamMD5 = new String(Base64.encodeBase64(resultByte));

            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(bytes.length);
            objectMetadata.setContentMD5(streamMD5);

            return new PutObjectRequest(
                    inMemoryArchive.getBucket(),
                    inMemoryArchive.makeObjectKey(),
                    new ByteArrayInputStream(bytes),
                    objectMetadata
            );
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }
}

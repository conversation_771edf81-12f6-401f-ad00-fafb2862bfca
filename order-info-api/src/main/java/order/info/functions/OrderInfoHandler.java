package order.info.functions;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import order.info.configuration.WebClientConfig;
import order.info.service.OmsApiGraphQLServiceImpl;
import order.info.service.OrderGatewayImpl;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import order.info.exception.OrderDataNotFoundException;
import org.json.JSONObject;

import java.util.Collections;

public class OrderInfoHandler implements RequestHandler<APIGatewayProxyRequestEvent, APIGatewayProxyResponseEvent> {
    private static final Logger LOGGER = LogManager.getLogger(OrderInfoHandler.class);
    private static final String ORDER_ID = "orderId";
    private final OrderGatewayImpl orderGatewayImpl;

    /**
     * Default constructor
     * this constructor must be present for the AWS Lambda to instantiate the handler
     * as the AWS Lambda does not instantiate the class using the constructor with parameters
     */
    public OrderInfoHandler() {
        this(new OrderGatewayImpl(new OmsApiGraphQLServiceImpl(new WebClientConfig().omsApiClient())));
    }

    /**
     * Although AWS Lambda does not use this constructor, it is useful for testing purposes and dependency injection
     * and chaining the constructor with the default constructor(above constructor)
     *
     * @param orderGatewayImpl
     */
    public OrderInfoHandler(OrderGatewayImpl orderGatewayImpl) {
        this.orderGatewayImpl = orderGatewayImpl;
    }

    @Override
    public APIGatewayProxyResponseEvent handleRequest(APIGatewayProxyRequestEvent input, Context context) {

        String orderId = input.getPathParameters().get(ORDER_ID);
        LOGGER.info("Invoked Order Information Handler for order: {}", orderId);
        JSONObject json;
        APIGatewayProxyResponseEvent responseEvent = new APIGatewayProxyResponseEvent();
        try {
            json = orderGatewayImpl.getOrderInfo(orderId);
            LOGGER.info("Successfully executed query.  Result: {}", json);

            responseEvent.setHeaders(Collections.singletonMap(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType()));
            responseEvent.setStatusCode(HttpStatus.SC_OK);
            responseEvent.setBody(json.toString());
        } catch (OrderDataNotFoundException e) {
            LOGGER.error("Caught OrderDataNotFoundException: ", e);
            responseEvent.setStatusCode(HttpStatus.SC_NOT_FOUND);
            responseEvent.setBody(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("Caught exception: ", e);
            responseEvent.setStatusCode(HttpStatus.SC_INTERNAL_SERVER_ERROR);
            responseEvent.setBody("An unhandled error has been raised.");
        }

        return responseEvent;
    }
}

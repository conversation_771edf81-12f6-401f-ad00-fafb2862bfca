package oiaua.functions;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UserAuthorizerHandler implements RequestHandler<APIGatewayProxyRequestEvent, Object> {
    private static final Logger LOGGER = LogManager.getLogger(UserAuthorizerHandler.class);
    private static final String JWT_GENERATOR_API_PATH = "login";

    @Override
    public Object handleRequest(APIGatewayProxyRequestEvent event, Context context) {
        LOGGER.info("Received Event: {}", event);
        Object response;

        if (event.getPath().contains(JWT_GENERATOR_API_PATH)) {
            JWTGeneratorHandler jwtGeneratorHandler = new JWTGeneratorHandler();
            response = jwtGeneratorHandler.generateJWT();
        } else {
            JWTValidatorHandler jwtValidatorHandler = new JWTValidatorHandler();
            response = jwtValidatorHandler.validateJWT(event);
        }
        return response;
    }
}
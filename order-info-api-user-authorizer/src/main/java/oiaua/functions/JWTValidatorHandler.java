package oiaua.functions;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.IamPolicyResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import oiaua.config.JWTConfig;
import oiaua.util.JWTUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class JWTValidatorHandler {
    private static final Logger LOGGER = LogManager.getLogger(JWTValidatorHandler.class);
    private static final String ACTION = "execute-api:Invoke";
    private static final String VERSION = "2012-10-17";
    private static final String AUTHORIZED = "Allow";
    private static final String NOT_AUTHORIZED = "Deny";
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String EXPIRATION_TIME = "expirationTime";
    private static final String MESSAGE = "message";
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String SUCCESS_MESSAGE = "Success";
    private static final String EXPIRED_INVALID_TOKEN_MESSAGE = "Token is expired or invalid";
    private static final String NULL_INVALID_FORMAT_TOKEN_MESSAGE = "Token is null or it has an invalid format";
    private static final String HEADER_NOT_FOUND_MESSAGE = "Header not found";

    protected IamPolicyResponse validateJWT(APIGatewayProxyRequestEvent event) {
        Map<String, String> headers = event.getHeaders();
        Map<String, Object> ctx = new HashMap<>();
        String auth = NOT_AUTHORIZED;

        if (headers != null && !headers.isEmpty()) {
            String authorizationToken = headers.get(AUTHORIZATION_HEADER);
            LOGGER.info("Received Token: {}", authorizationToken);

            if (authorizationToken != null && !authorizationToken.isEmpty() && authorizationToken.startsWith(BEARER_PREFIX)) {
                Date expirationTime = JWTUtil.validateAndGetExpirationTime(authorizationToken);
                Date currentTime = new Date();

                if (expirationTime != null && currentTime.before(expirationTime)) {
                    auth = AUTHORIZED;
                    ctx.put(MESSAGE, SUCCESS_MESSAGE);
                    ctx.put(EXPIRATION_TIME, expirationTime.toString());
                } else {
                    ctx.put(MESSAGE, EXPIRED_INVALID_TOKEN_MESSAGE);
                }
            } else {
                ctx.put(MESSAGE, NULL_INVALID_FORMAT_TOKEN_MESSAGE);
            }
        } else {
            ctx.put(MESSAGE, HEADER_NOT_FOUND_MESSAGE);
        }

        APIGatewayProxyRequestEvent.ProxyRequestContext proxyContext = event.getRequestContext();
        JWTConfig jwtConfig = new JWTConfig();
        String arn = String.format("arn:aws:execute-api:%s:%s:%s/%s/%s/%s", jwtConfig.getAwsRegion(), proxyContext.getAccountId(),
                proxyContext.getApiId(), proxyContext.getStage(), proxyContext.getHttpMethod(), "*");

        IamPolicyResponse.Statement policyStatement = IamPolicyResponse.Statement.builder()
                .withAction(ACTION).withEffect(auth).withResource(Collections.singletonList(arn)).build();
        IamPolicyResponse.PolicyDocument policyDocument1 = IamPolicyResponse.PolicyDocument.builder()
                .withVersion(VERSION).withStatement(Collections.singletonList(policyStatement)).build();

        IamPolicyResponse response = new IamPolicyResponse();
        response.setPrincipalId(jwtConfig.getSubject());
        response.setPolicyDocument(policyDocument1);
        response.setContext(ctx);
        logResponse(response);
        return response;
    }

    private void logResponse(IamPolicyResponse response) {
        try {
            ObjectMapper mapper = new ObjectMapper().enable(SerializationFeature.INDENT_OUTPUT);
            LOGGER.info("User Authorization Response: {}", mapper.writeValueAsString(response));
        } catch (Exception e) {
            LOGGER.error("Error while logging response.", e);
        }
    }
}

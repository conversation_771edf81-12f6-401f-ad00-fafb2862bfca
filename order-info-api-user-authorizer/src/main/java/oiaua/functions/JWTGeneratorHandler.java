package oiaua.functions;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import oiaua.util.JWTUtil;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;

public class JWTGeneratorHandler {
    private static final Logger LOGGER = LogManager.getLogger(JWTGeneratorHandler.class);
    private static final String ERROR_MESSAGE = "Error while generating token";

    protected APIGatewayProxyResponseEvent generateJWT() {
        APIGatewayProxyResponseEvent responseEvent = new APIGatewayProxyResponseEvent();

        try {
            responseEvent.setHeaders(Collections.singletonMap(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType()));
            responseEvent.setStatusCode(HttpStatus.SC_OK);
            responseEvent.setBody(JWTUtil.generateToken());
        } catch (Exception e) {
            responseEvent.setStatusCode(HttpStatus.SC_INTERNAL_SERVER_ERROR);
            responseEvent.setBody(ERROR_MESSAGE);
            LOGGER.error(ERROR_MESSAGE + ": ", e);
        }
        LOGGER.info("User Token Generator Response: {}", responseEvent);
        return responseEvent;
    }
}